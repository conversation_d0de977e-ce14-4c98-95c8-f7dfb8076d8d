<?php

namespace Tests\Unit;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Services\ReservationCostService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ReservationCostService::class)]
class ReservationCostServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ReservationCostService $service;

    protected Field $field;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new ReservationCostService;
        $this->field = Field::factory()->create([
            'hourly_rate' => 75.00,
            'name' => 'Test Soccer Field',
            'status' => 'Active', // Explicitly set status for predictable tests
        ]);
        $this->user = User::factory()->create();
    }

    #[Test]
    public function it_calculates_total_cost_correctly()
    {
        $cost = $this->service->calculateTotalCost($this->field, 3);
        $this->assertEquals(225.00, $cost); // 75 * 3
    }

    #[Test]
    public function it_provides_detailed_cost_breakdown()
    {
        $breakdown = $this->service->getCostBreakdown($this->field, 2);

        $this->assertArrayHasKey('field_name', $breakdown);
        $this->assertArrayHasKey('hourly_rate', $breakdown);
        $this->assertArrayHasKey('duration_hours', $breakdown);
        $this->assertArrayHasKey('subtotal', $breakdown);
        $this->assertArrayHasKey('tax_rate', $breakdown);
        $this->assertArrayHasKey('tax_amount', $breakdown);
        $this->assertArrayHasKey('total_cost', $breakdown);
        $this->assertArrayHasKey('formatted', $breakdown);

        $this->assertEquals('Test Soccer Field', $breakdown['field_name']);
        $this->assertEquals(75.00, $breakdown['hourly_rate']);
        $this->assertEquals(2, $breakdown['duration_hours']);
        $this->assertEquals(150.00, $breakdown['subtotal']);
        $this->assertEquals(0, $breakdown['tax_rate']); // No tax in Phase 1
        $this->assertEquals(0, $breakdown['tax_amount']);
        $this->assertEquals(150.00, $breakdown['total_cost']);

        // Check formatted values
        $this->assertEquals('$75.00', $breakdown['formatted']['hourly_rate']);
        $this->assertEquals('$150.00', $breakdown['formatted']['total_cost']);
    }

    #[Test]
    public function it_calculates_multi_slot_cost()
    {
        $otherField = Field::factory()->create(['hourly_rate' => 50.00]);

        $slots = [
            ['field_id' => $this->field->id, 'duration_hours' => 2],
            ['field_id' => $otherField->id, 'duration_hours' => 3],
        ];

        $result = $this->service->calculateMultiSlotCost($slots);

        $this->assertArrayHasKey('slots', $result);
        $this->assertArrayHasKey('total_cost', $result);
        $this->assertArrayHasKey('formatted_total', $result);

        $this->assertCount(2, $result['slots']);
        $this->assertEquals(300.00, $result['total_cost']); // (75*2) + (50*3) = 150 + 150
        $this->assertEquals('$300.00', $result['formatted_total']);
    }

    #[Test]
    public function it_provides_field_cost_comparison()
    {
        $cheaperField = Field::factory()->create(['hourly_rate' => 50.00, 'status' => 'Active']);
        $expensiveField = Field::factory()->create(['hourly_rate' => 100.00, 'status' => 'Active']);

        $comparison = $this->service->getFieldCostComparison(2);

        $this->assertIsArray($comparison);
        $this->assertCount(3, $comparison); // Including the field from setUp

        // Should be ordered by hourly rate (cheapest first)
        $this->assertEquals(50.00, $comparison[0]['cost_per_hour']);
        $this->assertEquals(75.00, $comparison[1]['cost_per_hour']);
        $this->assertEquals(100.00, $comparison[2]['cost_per_hour']);

        // Check cost calculation
        $this->assertEquals(100.00, $comparison[0]['cost']); // 50 * 2
        $this->assertEquals(150.00, $comparison[1]['cost']); // 75 * 2
        $this->assertEquals(200.00, $comparison[2]['cost']); // 100 * 2
    }

    #[Test]
    public function it_calculates_duration_savings()
    {
        $field = Field::factory()->create([
            'hourly_rate' => 60.00,
            'min_booking_hours' => 1,
            'max_booking_hours' => 4,
        ]);

        $savings = $this->service->getDurationSavings($field);

        $this->assertCount(4, $savings); // 1 to 4 hours

        // Check first duration (1 hour)
        $this->assertEquals(1, $savings[0]['duration']);
        $this->assertEquals(60.00, $savings[0]['total_cost']);
        $this->assertEquals(60.00, $savings[0]['cost_per_hour']);
        $this->assertFalse($savings[0]['is_best_value']); // Less than 3 hours

        // Check longer duration (3+ hours should be best value)
        $longerDuration = array_filter($savings, fn ($s) => $s['duration'] >= 3);
        foreach ($longerDuration as $duration) {
            $this->assertTrue($duration['is_best_value']);
        }
    }

    #[Test]
    public function it_calculates_monthly_cost_for_recurring_reservations()
    {
        $result = $this->service->calculateMonthlyCost($this->field, 2, 3); // 2 hours, 3 times per week

        $this->assertArrayHasKey('single_session_cost', $result);
        $this->assertArrayHasKey('weekly_cost', $result);
        $this->assertArrayHasKey('monthly_cost', $result);
        $this->assertArrayHasKey('sessions_per_week', $result);
        $this->assertArrayHasKey('sessions_per_month', $result);
        $this->assertArrayHasKey('formatted', $result);

        $this->assertEquals(150.00, $result['single_session_cost']); // 75 * 2
        $this->assertEquals(450.00, $result['weekly_cost']); // 150 * 3
        $this->assertEquals(1948.50, $result['monthly_cost']); // 450 * 4.33
        $this->assertEquals(3, $result['sessions_per_week']);
        $this->assertEquals(12.99, $result['sessions_per_month']); // 3 * 4.33

        $this->assertEquals('$150.00', $result['formatted']['single_session']);
        $this->assertEquals('$450.00', $result['formatted']['weekly']);
        $this->assertEquals('$1,948.50', $result['formatted']['monthly']);
    }

    #[Test]
    public function it_gets_user_cost_statistics()
    {
        $fromDate = Carbon::now()->startOfMonth();
        $toDate = Carbon::now()->endOfMonth();

        // Create test reservations
        Reservation::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->format('Y-m-d'),
            'status' => 'Confirmed',
            'total_cost' => 150.00,
            'duration_hours' => 2,
        ]);

        Reservation::factory()->create([
            'user_id' => $this->user->id,
            'field_id' => $this->field->id,
            'booking_date' => now()->format('Y-m-d'),
            'status' => 'Cancelled',
            'total_cost' => 150.00,
            'duration_hours' => 2,
        ]);

        $stats = $this->service->getUserCostStatistics($this->user->id, $fromDate, $toDate);

        $this->assertArrayHasKey('period', $stats);
        $this->assertArrayHasKey('totals', $stats);
        $this->assertArrayHasKey('formatted_totals', $stats);
        $this->assertArrayHasKey('cost_by_field', $stats);

        // Check totals (should exclude cancelled reservations)
        $this->assertEquals(450.00, $stats['totals']['total_cost']); // 3 * 150
        $this->assertEquals(6, $stats['totals']['total_hours']); // 3 * 2
        $this->assertEquals(3, $stats['totals']['total_reservations']);
        $this->assertEquals(75.00, $stats['totals']['average_cost_per_hour']); // 450 / 6
        $this->assertEquals(150.00, $stats['totals']['average_cost_per_reservation']); // 450 / 3

        // Check formatted values
        $this->assertEquals('$450.00', $stats['formatted_totals']['total_cost']);
        $this->assertEquals('$75.00', $stats['formatted_totals']['average_cost_per_hour']);
    }

    #[Test]
    public function it_calculates_peak_hour_cost()
    {
        // Test regular hour
        $result = $this->service->calculatePeakHourCost($this->field, 2, '10:00');
        $this->assertFalse($result['is_peak_hour']);
        $this->assertEquals(75.00, $result['base_rate']);
        $this->assertEquals(1.0, $result['peak_multiplier']);
        $this->assertEquals(75.00, $result['effective_rate']);
        $this->assertEquals(150.00, $result['total_cost']);

        // Test peak hour (6 PM - 9 PM)
        $result = $this->service->calculatePeakHourCost($this->field, 2, '19:00');
        $this->assertTrue($result['is_peak_hour']);
        $this->assertEquals(75.00, $result['base_rate']);
        $this->assertEquals(1.0, $result['peak_multiplier']); // No peak pricing in Phase 1
        $this->assertEquals(75.00, $result['effective_rate']);
        $this->assertEquals(150.00, $result['total_cost']);
    }

    #[Test]
    public function it_validates_cost_calculation()
    {
        $reservation = Reservation::factory()->create([
            'field_id' => $this->field->id,
            'duration_hours' => 3,
            'total_cost' => 225.00, // Correct cost
        ]);

        $validation = $this->service->validateCostCalculation($reservation);
        $this->assertTrue($validation['is_valid']);
        $this->assertEquals(225.00, $validation['expected_cost']);
        $this->assertEquals(225.00, $validation['actual_cost']);
        $this->assertEquals(0.00, $validation['difference']);

        // Test incorrect cost
        $reservation->update(['total_cost' => 200.00]);
        $validation = $this->service->validateCostCalculation($reservation);
        $this->assertFalse($validation['is_valid']);
        $this->assertEquals(225.00, $validation['expected_cost']);
        $this->assertEquals(200.00, $validation['actual_cost']);
        $this->assertEquals(-25.00, $validation['difference']);
    }

    #[Test]
    public function it_gets_reservation_estimate()
    {
        $estimate = $this->service->getReservationEstimate($this->field->id, 2, '14:00');

        $this->assertArrayHasKey('field_name', $estimate);
        $this->assertArrayHasKey('hourly_rate', $estimate);
        $this->assertArrayHasKey('duration_hours', $estimate);
        $this->assertArrayHasKey('total_cost', $estimate);
        $this->assertArrayHasKey('formatted', $estimate);
        $this->assertArrayHasKey('peak_hour_info', $estimate);

        $this->assertEquals('Test Soccer Field', $estimate['field_name']);
        $this->assertEquals(75.00, $estimate['hourly_rate']);
        $this->assertEquals(2, $estimate['duration_hours']);
        $this->assertEquals(150.00, $estimate['total_cost']);
    }
}
