<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(Booking::class)]
class BookingEditViewTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->employee = User::factory()->create(['role' => 'employee']);
    }

    /**
     * Test that booking edit page loads correctly with admin layout
     */
    public function test_booking_edit_page_loads_with_correct_layout(): void
    {
        // Create a field and booking
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        // Test as admin
        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);
        $response->assertViewIs('bookings.edit');
        $response->assertViewHas('booking', $booking);
        $response->assertViewHas('fields');

        // Check that the page contains Bootstrap classes (not Tailwind)
        $response->assertSee('form-control');
        $response->assertSee('btn btn-primary');
        $response->assertSee('card custom-card');

        // Check that it doesn't contain Tailwind classes
        $response->assertDontSee('bg-gray-50');
        $response->assertDontSee('text-gray-700');
        $response->assertDontSee('border-gray-300');
    }

    /**
     * Test that booking owner can access edit page
     */
    public function test_booking_owner_can_access_edit_page(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->employee)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);
        $response->assertViewIs('bookings.edit');
    }

    /**
     * Test that unauthorized users cannot access edit page
     */
    public function test_unauthorized_user_cannot_access_edit_page(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
        ]);

        $otherUser = User::factory()->create(['role' => 'employee']);

        $response = $this->actingAs($otherUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(403);
    }

    /**
     * Test that the edit form contains all required fields
     */
    public function test_edit_form_contains_required_fields(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);

        // Check for form fields
        $response->assertSee('name="field_id"', false);
        $response->assertSee('name="booking_date"', false);
        $response->assertSee('name="start_time"', false);
        $response->assertSee('name="duration_hours"', false);
        $response->assertSee('name="customer_name"', false);
        $response->assertSee('name="customer_email"', false);
        $response->assertSee('name="customer_phone"', false);
        $response->assertSee('name="special_requests"', false);

        // Check for CSRF token
        $response->assertSee('name="_token"', false);
        $response->assertSee('name="_method"', false);
        $response->assertSee('value="PUT"', false);
    }

    /**
     * Test that the edit form shows current booking data
     */
    public function test_edit_form_shows_current_booking_data(): void
    {
        $field = Field::factory()->create(['name' => 'Test Soccer Field']);
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '************',
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);

        // Check that current data is displayed
        $response->assertSee('Test Soccer Field');
        $response->assertSee('John Doe');
        $response->assertSee('<EMAIL>');
        $response->assertSee('************');
        $response->assertSee($booking->status);
    }

    /**
     * Test that JavaScript functions are included
     */
    public function test_javascript_functions_are_included(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertStatus(200);

        // Check for JavaScript functions
        $response->assertSee('function updateFieldInfo()');
        $response->assertSee('function calculateCost()');
        $response->assertSee('DOMContentLoaded');
    }

    /**
     * Test that past bookings cannot be edited
     */
    public function test_past_bookings_cannot_be_edited(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Pending',
            'booking_date' => now()->subDays(1)->format('Y-m-d'), // Past date
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'This booking cannot be edited.');
    }

    /**
     * Test that completed bookings cannot be edited
     */
    public function test_completed_bookings_cannot_be_edited(): void
    {
        $field = Field::factory()->create();
        $booking = Booking::factory()->create([
            'user_id' => $this->employee->id,
            'field_id' => $field->id,
            'status' => 'Completed',
            'booking_date' => now()->addDays(1)->format('Y-m-d'),
        ]);

        $response = $this->actingAs($this->adminUser)
            ->get(route('bookings.edit', $booking));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'This booking cannot be edited.');
    }
}
