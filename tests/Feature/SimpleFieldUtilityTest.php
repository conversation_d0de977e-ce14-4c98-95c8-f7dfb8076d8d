<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\User;
use App\Models\Utility;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(Field::class)]
class SimpleFieldUtilityTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test admin user
        $this->admin = User::factory()->create(['role' => 'admin']);
    }

    #[Test]
    public function field_utility_relationship_works()
    {
        // Create a field and utility
        $field = Field::factory()->create();
        $utility = Utility::factory()->create(['is_active' => true]);

        // Attach utility to field
        $field->utilities()->attach($utility);

        // Test the relationship
        $this->assertTrue($field->utilities->contains($utility));
        $this->assertTrue($utility->fields->contains($field));
        $this->assertEquals(1, $field->utilities->count());
        $this->assertEquals(1, $utility->fields->count());
    }

    #[Test]
    public function admin_can_create_field_with_utilities()
    {
        // Create utilities
        $utility1 = Utility::factory()->create(['is_active' => true]);
        $utility2 = Utility::factory()->create(['is_active' => true]);

        $fieldData = [
            'name' => 'Test Field with Utilities',
            'type' => 'Soccer',
            'description' => 'A test field with utilities',
            'hourly_rate' => 50.00,
            'capacity' => 20,
            'status' => 'Active',
            'utilities' => [$utility1->id, $utility2->id],
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/fields', $fieldData);

        $response->assertRedirect('/admin/fields');

        // Verify field was created with utilities
        $field = Field::where('name', 'Test Field with Utilities')->first();
        $this->assertNotNull($field);
        $this->assertEquals(2, $field->utilities->count());
        $this->assertTrue($field->utilities->contains($utility1));
        $this->assertTrue($field->utilities->contains($utility2));
    }

    #[Test]
    public function field_show_page_loads_without_errors()
    {
        // Create field with utilities
        $field = Field::factory()->create();
        $utility = Utility::factory()->create(['is_active' => true]);
        $field->utilities()->attach($utility);

        $response = $this->actingAs($this->admin)
            ->get("/admin/fields/{$field->id}");

        $response->assertStatus(200);
        $response->assertSee($field->name);
        $response->assertSee('Available Utilities');
    }

    #[Test]
    public function field_create_form_shows_utilities()
    {
        // Create some utilities
        $utility1 = Utility::factory()->create(['is_active' => true]);
        $utility2 = Utility::factory()->create(['is_active' => false]);

        $response = $this->actingAs($this->admin)
            ->get('/admin/fields/create');

        $response->assertStatus(200);
        $response->assertSee('Available Utilities');
        $response->assertSee($utility1->name); // Active utility should be shown
        $response->assertDontSee($utility2->name); // Inactive utility should not be shown
    }

    #[Test]
    public function field_edit_form_shows_utilities()
    {
        // Create field with utilities
        $field = Field::factory()->create();
        $utility = Utility::factory()->create(['is_active' => true]);
        $field->utilities()->attach($utility);

        $response = $this->actingAs($this->admin)
            ->get("/admin/fields/{$field->id}/edit");

        $response->assertStatus(200);
        $response->assertSee('Available Utilities');
        $response->assertSee($utility->name);
    }
}
