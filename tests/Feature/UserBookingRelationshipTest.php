<?php

namespace Tests\Feature;

use App\Models\Booking;
use App\Models\Field;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(User::class)]
class UserBookingRelationshipTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function user_has_bookings_relationship(): void
    {
        // Create a user
        $user = User::factory()->create(['role' => 'employee']);

        // Create a field
        $field = Field::factory()->create();

        // Create bookings for the user
        $booking1 = Booking::factory()->create([
            'user_id' => $user->id,
            'field_id' => $field->id,
        ]);

        $booking2 = Booking::factory()->create([
            'user_id' => $user->id,
            'field_id' => $field->id,
        ]);

        // Test the relationship
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $user->bookings);
        $this->assertCount(2, $user->bookings);
        $this->assertTrue($user->bookings->contains($booking1));
        $this->assertTrue($user->bookings->contains($booking2));
    }

    #[Test]
    public function user_bookings_count(): void
    {
        // Create a user
        $user = User::factory()->create(['role' => 'employee']);

        // Create a field
        $field = Field::factory()->create();

        // Initially should have 0 bookings
        $this->assertEquals(0, $user->bookings()->count());

        // Create 3 bookings for the user
        Booking::factory()->count(3)->create([
            'user_id' => $user->id,
            'field_id' => $field->id,
        ]);

        // Should now have 3 bookings
        $this->assertEquals(3, $user->bookings()->count());
    }

    #[Test]
    public function user_admin_bookings_relationship(): void
    {
        // Create an admin user
        $admin = User::factory()->create(['role' => 'admin']);

        // Create a regular user
        $user = User::factory()->create(['role' => 'employee']);

        // Create a field
        $field = Field::factory()->create();

        // Create a booking made by admin for the user
        $booking = Booking::factory()->create([
            'user_id' => $user->id,
            'booked_by' => $admin->id,
            'field_id' => $field->id,
        ]);

        // Test the admin bookings relationship
        $this->assertCount(1, $admin->adminBookings);
        $this->assertTrue($admin->adminBookings->contains($booking));

        // Test that the user has the booking but admin doesn't in regular bookings
        $this->assertCount(1, $user->bookings);
        $this->assertCount(0, $admin->bookings);
    }

    #[Test]
    public function features_page_can_access_user_bookings_count(): void
    {
        // Create an employee user
        $user = User::factory()->create(['role' => 'employee']);

        // Create a field
        $field = Field::factory()->create();

        // Create some bookings for the user
        Booking::factory()->count(2)->create([
            'user_id' => $user->id,
            'field_id' => $field->id,
        ]);

        // Access the features page
        $response = $this->actingAs($user)
            ->get(route('employee.features'));

        $response->assertStatus(200);
        $response->assertSee('Total Bookings:');
        $response->assertSee('2'); // Should show the count of bookings
    }

    #[Test]
    public function features_page_works_for_users_with_no_bookings(): void
    {
        // Create an employee user with no bookings
        $user = User::factory()->create(['role' => 'employee']);

        // Access the features page
        $response = $this->actingAs($user)
            ->get(route('employee.features'));

        $response->assertStatus(200);
        $response->assertSee('Total Bookings:');
        $response->assertSee('0'); // Should show 0 bookings
    }
}
