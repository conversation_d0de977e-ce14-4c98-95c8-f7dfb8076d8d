<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(User::class)]
class AdminUserEditTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an admin user for testing
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
        ]);
    }

    /**
     * Test that admin can access user edit page
     */
    public function test_admin_can_access_user_edit_page(): void
    {
        $user = User::factory()->create(['role' => 'employee']);

        $response = $this->actingAs($this->adminUser)
            ->get(route('admin.users.edit', $user));

        $response->assertStatus(200);
        $response->assertViewIs('admin.users.edit');
        $response->assertViewHas('user', $user);
    }

    /**
     * Test that non-admin cannot access user edit page
     */
    public function test_non_admin_cannot_access_user_edit_page(): void
    {
        $employee = User::factory()->create(['role' => 'employee']);
        $userToEdit = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($employee)
            ->get(route('admin.users.edit', $userToEdit));

        $response->assertStatus(403);
    }

    /**
     * Test admin can update user basic information
     */
    public function test_admin_can_update_user_basic_information(): void
    {
        $user = User::factory()->create([
            'name' => 'Old Name',
            'email' => '<EMAIL>',
            'username' => 'oldusername',
            'role' => 'employee',
        ]);

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), [
                'name' => 'New Name',
                'email' => '<EMAIL>',
                'username' => 'newusername',
                'role' => 'user',
                'account_status' => 'active',
            ]);

        $response->assertRedirect(route('admin.users.show', $user));
        $response->assertSessionHas('success', 'User updated successfully.');

        $user->refresh();
        $this->assertEquals('New Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('newusername', $user->username);
        $this->assertEquals('user', $user->role);
    }

    /**
     * Test admin can update user password
     */
    public function test_admin_can_update_user_password(): void
    {
        $user = User::factory()->create([
            'password' => Hash::make('oldpassword'),
            'role' => 'employee',
        ]);

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'role' => $user->role,
                'account_status' => 'active',
                'password' => 'newpassword',
                'password_confirmation' => 'newpassword',
            ]);

        $response->assertRedirect(route('admin.users.show', $user));

        $user->refresh();
        $this->assertTrue(Hash::check('newpassword', $user->password));
    }

    /**
     * Test admin can lock user account
     */
    public function test_admin_can_lock_user_account(): void
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'locked_until' => null,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'role' => $user->role,
                'account_status' => 'locked',
            ]);

        $response->assertRedirect(route('admin.users.show', $user));

        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);
    }

    /**
     * Test admin can unlock user account
     */
    public function test_admin_can_unlock_user_account(): void
    {
        $user = User::factory()->create([
            'role' => 'employee',
            'locked_until' => now()->addHours(24),
            'failed_login_attempts' => 5,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'role' => $user->role,
                'account_status' => 'active',
            ]);

        $response->assertRedirect(route('admin.users.show', $user));

        $user->refresh();
        $this->assertFalse($user->isLocked());
        $this->assertEquals(0, $user->failed_login_attempts);
    }

    /**
     * Test admin cannot remove their own admin role
     */
    public function test_admin_cannot_remove_own_admin_role(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $this->adminUser), [
                'name' => $this->adminUser->name,
                'email' => $this->adminUser->email,
                'username' => $this->adminUser->username,
                'role' => 'employee', // Trying to change own role
                'account_status' => 'active',
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'You cannot remove your own admin privileges.');

        $this->adminUser->refresh();
        $this->assertEquals('admin', $this->adminUser->role);
    }

    /**
     * Test admin cannot lock their own account
     */
    public function test_admin_cannot_lock_own_account(): void
    {
        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $this->adminUser), [
                'name' => $this->adminUser->name,
                'email' => $this->adminUser->email,
                'username' => $this->adminUser->username,
                'role' => $this->adminUser->role,
                'account_status' => 'locked', // Trying to lock own account
            ]);

        $response->assertRedirect();
        $response->assertSessionHas('error', 'You cannot lock your own account.');

        $this->adminUser->refresh();
        $this->assertFalse($this->adminUser->isLocked());
    }

    /**
     * Test validation errors are handled properly
     */
    public function test_validation_errors_are_handled(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), [
                'name' => '', // Required field empty
                'email' => 'invalid-email', // Invalid email
                'role' => 'invalid_role', // Invalid role
                'account_status' => 'invalid_status', // Invalid status
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['name', 'email', 'role', 'account_status']);
    }

    /**
     * Test password confirmation validation
     */
    public function test_password_confirmation_validation(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($this->adminUser)
            ->put(route('admin.users.update', $user), [
                'name' => $user->name,
                'email' => $user->email,
                'username' => $user->username,
                'role' => $user->role,
                'account_status' => 'active',
                'password' => 'newpassword',
                'password_confirmation' => 'differentpassword', // Mismatched confirmation
            ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['password']);
    }
}
