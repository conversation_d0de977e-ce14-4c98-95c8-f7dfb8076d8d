<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(User::class)]
class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test login with email
     */
    public function test_login_with_email(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'employee',
        ]);

        $response = $this->post('/login', [
            'login' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    /**
     * Test login with username
     */
    public function test_login_with_username(): void
    {
        $user = User::factory()->create([
            'username' => 'testuser',
            'password' => Hash::make('password'),
            'role' => 'employee',
        ]);

        $response = $this->post('/login', [
            'login' => 'testuser',
            'password' => 'password',
        ]);

        $response->assertRedirect('/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    /**
     * Test admin middleware protection
     */
    public function test_admin_middleware_protection(): void
    {
        $employee = User::factory()->create(['role' => 'employee']);

        $this->actingAs($employee)
            ->get('/admin/users')
            ->assertStatus(403);
    }

    /**
     * Test admin access to user management
     */
    public function test_admin_access_to_user_management(): void
    {
        $adminUser = User::factory()->create(['role' => 'admin']);

        $this->actingAs($adminUser)
            ->get('/admin/users')
            ->assertStatus(200);
    }

    /**
     * Test account lockout after failed attempts
     */
    public function test_account_lockout_after_failed_attempts(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'employee',
        ]);

        // Make 5 failed login attempts
        for ($i = 0; $i < 5; $i++) {
            $this->post('/login', [
                'login' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]);
        }

        $user->refresh();
        $this->assertTrue($user->isLocked());
        $this->assertEquals(5, $user->failed_login_attempts);
    }

    /**
     * Test role-based dashboard redirection
     */
    public function test_role_based_dashboard_redirection(): void
    {
        $adminUser = User::factory()->create(['role' => 'admin']);
        $employeeUser = User::factory()->create(['role' => 'employee']);
        $userUser = User::factory()->create(['role' => 'user']);

        // Test admin redirection
        $this->actingAs($adminUser)
            ->get('/dashboard')
            ->assertRedirect('/admin/dashboard');

        // Test employee redirection
        $this->actingAs($employeeUser)
            ->get('/dashboard')
            ->assertRedirect('/employee/dashboard');

        // Test user redirection
        $this->actingAs($userUser)
            ->get('/dashboard')
            ->assertRedirect('/user/dashboard');
    }
}
