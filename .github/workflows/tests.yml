name: tests

on:
  push:
    branches:
      - develop
      - main
  pull_request:
    branches:
      - develop
      - main

permissions:
  contents: read
  issues: read
  checks: write
  pull-requests: write

jobs:
  test-ci:
    runs-on: ubuntu-latest
    # environment: Testing

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.4
          tools: composer:v2
          coverage: xdebug

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Install Node Dependencies
        run: npm i

      - name: Install Dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader

      - name: Copy Environment File
        run: cp .env.example .env

      - name: Generate Application Key
        run: php artisan key:generate

      - name: Build Assets
        run: npm run build

      - name: Run Tests with Coverage
        run: ./vendor/bin/phpunit --coverage-clover=ci-runs/clover.xml --coverage-html=coverage-report

      - name: Publish PHPUnit Test Results
        if: always()
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
         check_name: "PHPUnit Test Results"
         files: ci-runs/phpunit-results.xml

      - name: Upload Coverage Report to Codecov
        if: always()
        uses: codecov/codecov-action@v3
        with:
          files: ci-runs/clover.xml
          fail_ci_if_error: false
          verbose: true

      - name: Upload Coverage Report as Artifact
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: coverage-report
          path: coverage-report/